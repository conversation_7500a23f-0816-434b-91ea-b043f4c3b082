# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-current-monitor"
description = "Enables the current_monitor command without any pre-configured scope."
commands.allow = ["current_monitor"]

[[permission]]
identifier = "deny-current-monitor"
description = "Denies the current_monitor command without any pre-configured scope."
commands.deny = ["current_monitor"]
