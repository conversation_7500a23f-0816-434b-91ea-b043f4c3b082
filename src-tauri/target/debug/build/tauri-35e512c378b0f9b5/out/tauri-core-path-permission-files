["/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/basename.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/dirname.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/extname.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/is_absolute.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/join.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/normalize.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/resolve.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/commands/resolve_directory.toml", "/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-35e512c378b0f9b5/out/permissions/path/autogenerated/default.toml"]