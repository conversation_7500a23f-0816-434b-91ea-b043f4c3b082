cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-plugin-shell-5bdfba4900591ad2/out/tauri-plugin-shell-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/tauri-plugin-shell-5bdfba4900591ad2/out/global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-shell-2.2.1/api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
