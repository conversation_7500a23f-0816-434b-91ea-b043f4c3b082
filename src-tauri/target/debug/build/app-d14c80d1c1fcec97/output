cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=app
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_playlistplatformer
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Documents/augment-projects/Playlist Platformer/src-tauri/target/debug/build/app-d14c80d1c1fcec97/out/app-manifest/__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=aarch64-apple-darwin
cargo:rustc-env=MACOSX_DEPLOYMENT_TARGET=10.13
