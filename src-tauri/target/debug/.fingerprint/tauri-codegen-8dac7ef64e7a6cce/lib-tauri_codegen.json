{"rustc": 15497389221046826682, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 11533600920475303313, "deps": [[2616743947975331138, "plist", false, 3682084328881511429], [3060637413840920116, "proc_macro2", false, 11520194141832687593], [3150220818285335163, "url", false, 13787492539808758573], [4899080583175475170, "semver", false, 13061391140959969752], [7170110829644101142, "json_patch", false, 8920943465255171492], [7392050791754369441, "ico", false, 13316920385077382562], [8319709847752024821, "uuid", false, 10145844372396431184], [9689903380558560274, "serde", false, 3911140700534641940], [9857275760291862238, "sha2", false, 1555058312724214519], [10640660562325816595, "syn", false, 13341899249629061802], [10806645703491011684, "thiserror", false, 15016214148651253432], [11050281405049894993, "tauri_utils", false, 10605988269562020991], [12409575957772518135, "time", false, 4678975853719089655], [12687914511023397207, "png", false, 14391288681644314814], [13077212702700853852, "base64", false, 8262103675807897467], [14132538657330703225, "brotli", false, 14923513558257473290], [15367738274754116744, "serde_json", false, 1338198806591924806], [15622660310229662834, "walkdir", false, 8633821404424625841], [17990358020177143287, "quote", false, 12146113348399126031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-8dac7ef64e7a6cce/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}