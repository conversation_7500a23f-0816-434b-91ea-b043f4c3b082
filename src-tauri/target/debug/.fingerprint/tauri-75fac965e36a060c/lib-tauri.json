{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 18127786013138035430, "deps": [[40386456601120721, "percent_encoding", false, 2499288624308541304], [442785307232013896, "tauri_runtime", false, 13796237545543223153], [1200537532907108615, "url<PERSON><PERSON>n", false, 11776028740191842374], [1386409696764982933, "objc2", false, 9814518012932224191], [2616743947975331138, "plist", false, 14001184563032330488], [3150220818285335163, "url", false, 13287345710003828363], [4143744114649553716, "raw_window_handle", false, 196020354320396666], [4341921533227644514, "muda", false, 9847463353242611605], [4919829919303820331, "serialize_to_javascript", false, 7995659944979992635], [5986029879202738730, "log", false, 13517631300886753083], [7752760652095876438, "tauri_runtime_wry", false, 5706303164717494919], [8589231650440095114, "embed_plist", false, 17288946656552610050], [9010263965687315507, "http", false, 8361839584996066817], [9228235415475680086, "tauri_macros", false, 8423861574049929228], [9538054652646069845, "tokio", false, 2164667913145525563], [9689903380558560274, "serde", false, 6634913462710143074], [9859211262912517217, "objc2_foundation", false, 9326045806118586156], [9920160576179037441, "getrandom", false, 3148621975556382432], [10229185211513642314, "mime", false, 4679492081772102237], [10575598148575346675, "objc2_app_kit", false, 14097684320850006289], [10629569228670356391, "futures_util", false, 14137886163956842138], [10755362358622467486, "build_script_build", false, 2584156159330900804], [10806645703491011684, "thiserror", false, 15016214148651253432], [11050281405049894993, "tauri_utils", false, 13056823868119026934], [11989259058781683633, "dunce", false, 11512646038938628873], [12565293087094287914, "window_vibrancy", false, 13479995625075940290], [12986574360607194341, "serde_repr", false, 4393998212103848715], [13077543566650298139, "heck", false, 17539410851212178363], [13625485746686963219, "anyhow", false, 4669025522612569633], [15367738274754116744, "serde_json", false, 2450685546078384675], [16928111194414003569, "dirs", false, 3661861156400743129], [17155886227862585100, "glob", false, 562982324261335501]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-75fac965e36a060c/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}