{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 16044682043466294854, "deps": [[442785307232013896, "tauri_runtime", false, 13796237545543223153], [1386409696764982933, "objc2", false, 9814518012932224191], [3150220818285335163, "url", false, 13287345710003828363], [4143744114649553716, "raw_window_handle", false, 196020354320396666], [5986029879202738730, "log", false, 13517631300886753083], [7752760652095876438, "build_script_build", false, 16760486597202743243], [9010263965687315507, "http", false, 8361839584996066817], [9859211262912517217, "objc2_foundation", false, 9326045806118586156], [10575598148575346675, "objc2_app_kit", false, 14097684320850006289], [11050281405049894993, "tauri_utils", false, 13056823868119026934], [13223659721939363523, "tao", false, 8631126573856349879], [14794439852947137341, "wry", false, 6818465308447079605]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-593ca41e56007e83/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}