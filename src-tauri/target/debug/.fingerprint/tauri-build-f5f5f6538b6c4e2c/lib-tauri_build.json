{"rustc": 15497389221046826682, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 2009673083043832314, "deps": [[4899080583175475170, "semver", false, 13061391140959969752], [6913375703034175521, "schemars", false, 16407380997056014883], [7170110829644101142, "json_patch", false, 8920943465255171492], [9689903380558560274, "serde", false, 3911140700534641940], [11050281405049894993, "tauri_utils", false, 10605988269562020991], [12714016054753183456, "tauri_winres", false, 13105583807786572486], [13077543566650298139, "heck", false, 17539410851212178363], [13475171727366188400, "cargo_toml", false, 13155839171986175968], [13625485746686963219, "anyhow", false, 4669025522612569633], [15367738274754116744, "serde_json", false, 1338198806591924806], [15609422047640926750, "toml", false, 3768793595121984433], [15622660310229662834, "walkdir", false, 8633821404424625841], [16928111194414003569, "dirs", false, 3661861156400743129], [17155886227862585100, "glob", false, 562982324261335501]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-f5f5f6538b6c4e2c/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}