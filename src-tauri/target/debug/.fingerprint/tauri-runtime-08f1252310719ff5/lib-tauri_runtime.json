{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 647725472442656799, "deps": [[442785307232013896, "build_script_build", false, 12742182580451266716], [3150220818285335163, "url", false, 13287345710003828363], [4143744114649553716, "raw_window_handle", false, 196020354320396666], [7606335748176206944, "dpi", false, 16961040906633922683], [9010263965687315507, "http", false, 8361839584996066817], [9689903380558560274, "serde", false, 6634913462710143074], [10806645703491011684, "thiserror", false, 15016214148651253432], [11050281405049894993, "tauri_utils", false, 13056823868119026934], [15367738274754116744, "serde_json", false, 2450685546078384675], [16727543399706004146, "cookie", false, 13674008879257669844]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-08f1252310719ff5/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}