{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 2841922440011452962, "deps": [[1615478164327904835, "pin_utils", false, 4133600941412668778], [1906322745568073236, "pin_project_lite", false, 6712396796791920259], [6955678925937229351, "slab", false, 16374450534590849203], [7620660491849607393, "futures_core", false, 11522897231410853943], [10565019901765856648, "futures_macro", false, 16312162194195470131], [16240732885093539806, "futures_task", false, 13625341524908217115]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-a4ae54eee5ebea09/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}