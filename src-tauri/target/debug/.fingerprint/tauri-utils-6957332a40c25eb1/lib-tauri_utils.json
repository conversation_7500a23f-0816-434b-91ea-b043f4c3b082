{"rustc": 15497389221046826682, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 3033921117576893, "path": 7977403231559494832, "deps": [[561782849581144631, "html5ever", false, 8481637532066131116], [1200537532907108615, "url<PERSON><PERSON>n", false, 3055488352461416875], [3060637413840920116, "proc_macro2", false, 11520194141832687593], [3150220818285335163, "url", false, 13787492539808758573], [3191507132440681679, "serde_untagged", false, 872601853809276455], [4899080583175475170, "semver", false, 13061391140959969752], [5986029879202738730, "log", false, 12707097318041091084], [6213549728662707793, "serde_with", false, 5380049803629041523], [6262254372177975231, "kuchiki", false, 4733540294317973869], [6606131838865521726, "ctor", false, 10972652934605368874], [6913375703034175521, "schemars", false, 16407380997056014883], [7170110829644101142, "json_patch", false, 8920943465255171492], [8319709847752024821, "uuid", false, 10145844372396431184], [9010263965687315507, "http", false, 8361839584996066817], [9451456094439810778, "regex", false, 4299037736249060348], [9689903380558560274, "serde", false, 3911140700534641940], [10806645703491011684, "thiserror", false, 15016214148651253432], [11655476559277113544, "cargo_metadata", false, 15121380787597572769], [11989259058781683633, "dunce", false, 11512646038938628873], [13625485746686963219, "anyhow", false, 4669025522612569633], [14132538657330703225, "brotli", false, 14923513558257473290], [14885200901422974105, "swift_rs", false, 4026315350110406213], [15367738274754116744, "serde_json", false, 1338198806591924806], [15609422047640926750, "toml", false, 3768793595121984433], [15622660310229662834, "walkdir", false, 8633821404424625841], [15932120279885307830, "memchr", false, 16067265597352771720], [17146114186171651583, "infer", false, 10962981756223501931], [17155886227862585100, "glob", false, 562982324261335501], [17186037756130803222, "phf", false, 15869505977305626358], [17990358020177143287, "quote", false, 12146113348399126031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-6957332a40c25eb1/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}