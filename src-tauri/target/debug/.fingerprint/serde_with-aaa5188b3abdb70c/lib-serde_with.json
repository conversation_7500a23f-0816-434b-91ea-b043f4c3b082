{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 3280471839685724059, "path": 10361435884929435479, "deps": [[6158493786865284961, "serde_with_macros", false, 16649563174130839963], [9689903380558560274, "serde", false, 6634913462710143074], [16257276029081467297, "serde_derive", false, 13432124480221447077]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/serde_with-aaa5188b3abdb70c/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}