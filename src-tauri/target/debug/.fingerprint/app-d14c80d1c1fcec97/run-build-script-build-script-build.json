{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 12123884807320663078], [10755362358622467486, "build_script_build", false, 2584156159330900804], [7236291379133587555, "build_script_build", false, 4411081298332373327], [1582828171158827377, "build_script_build", false, 2278950827725752498]], "local": [{"RerunIfChanged": {"output": "debug/build/app-d14c80d1c1fcec97/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}