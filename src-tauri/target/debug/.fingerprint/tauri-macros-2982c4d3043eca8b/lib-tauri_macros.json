{"rustc": 15497389221046826682, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 1198035839711529311, "deps": [[3060637413840920116, "proc_macro2", false, 11520194141832687593], [7341521034400937459, "tauri_codegen", false, 1043155141591250658], [10640660562325816595, "syn", false, 13341899249629061802], [11050281405049894993, "tauri_utils", false, 10605988269562020991], [13077543566650298139, "heck", false, 17539410851212178363], [17990358020177143287, "quote", false, 12146113348399126031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-2982c4d3043eca8b/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}