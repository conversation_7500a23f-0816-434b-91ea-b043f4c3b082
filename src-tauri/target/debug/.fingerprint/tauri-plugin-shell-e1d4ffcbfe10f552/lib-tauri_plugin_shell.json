{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 11308757308657882410, "deps": [[500211409582349667, "shared_child", false, 12011505773864391346], [1582828171158827377, "build_script_build", false, 2278950827725752498], [5986029879202738730, "log", false, 13517631300886753083], [9451456094439810778, "regex", false, 4299037736249060348], [9538054652646069845, "tokio", false, 2164667913145525563], [9689903380558560274, "serde", false, 6634913462710143074], [10755362358622467486, "tauri", false, 18130955353038458344], [10806645703491011684, "thiserror", false, 15016214148651253432], [11337703028400419576, "os_pipe", false, 11097686491585446659], [14564311161534545801, "encoding_rs", false, 14897020818206116180], [15367738274754116744, "serde_json", false, 2450685546078384675], [16192041687293812804, "open", false, 5668569370485554209]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-e1d4ffcbfe10f552/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}