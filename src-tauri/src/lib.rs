mod spotify;

use spotify::{auth::SpotifyAuth, callback_server::CallbackServer, config::SpotifyConfig};

#[tauri::command]
async fn spotify_auth_url() -> Result<(String, String, String), String> {
    let config = SpotifyConfig::load().map_err(|e| e.to_string())?;
    let auth = SpotifyAuth::new(config);
    Ok(auth.generate_auth_url())
}

#[tauri::command]
async fn spotify_exchange_code(code: String, code_verifier: String) -> Result<String, String> {
    let config = SpotifyConfig::load().map_err(|e| e.to_string())?;
    let auth = SpotifyAuth::new(config);
    let auth_state = auth
        .exchange_code(&code, &code_verifier)
        .await
        .map_err(|e| e.to_string())?;
    serde_json::to_string(&auth_state).map_err(|e| e.to_string())
}

#[tauri::command]
async fn spotify_authenticate(app: tauri::AppHandle) -> Result<String, String> {
    let config = SpotifyConfig::load().map_err(|e| e.to_string())?;
    let auth = SpotifyAuth::new(config);
    let (auth_url, expected_state, code_verifier) = auth.generate_auth_url();

    // Start callback server
    let callback_server = CallbackServer::new(8080);

    // Open auth URL in browser using opener plugin
    tauri_plugin_opener::open_url(&app, Some(&auth_url)).map_err(|e| e.to_string())?;

    // Wait for callback
    let callback_result = callback_server
        .start_and_wait()
        .await
        .map_err(|e| e.to_string())?;

    // Validate callback
    if let Some(error) = callback_result.error {
        return Err(format!("Authentication error: {}", error));
    }

    let code = callback_result
        .code
        .ok_or("No authorization code received")?;
    let state = callback_result.state.ok_or("No state parameter received")?;

    if state != expected_state {
        return Err("State mismatch - possible CSRF attack".to_string());
    }

    // Exchange code for tokens
    let auth_state = auth
        .exchange_code(&code, &code_verifier)
        .await
        .map_err(|e| e.to_string())?;
    serde_json::to_string(&auth_state).map_err(|e| e.to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            spotify_auth_url,
            spotify_exchange_code,
            spotify_authenticate
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
