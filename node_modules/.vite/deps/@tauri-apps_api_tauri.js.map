{"version": 3, "sources": ["../../@tauri-apps/api/tauri.js"], "sourcesContent": ["// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** @ignore */\nfunction uid() {\n    return window.crypto.getRandomValues(new Uint32Array(1))[0];\n}\n/**\n * Transforms a callback function to a string identifier that can be passed to the backend.\n * The backend uses the identifier to `eval()` the callback.\n *\n * @return A unique identifier associated with the callback function.\n *\n * @since 1.0.0\n */\nfunction transformCallback(callback, once = false) {\n    const identifier = uid();\n    const prop = `_${identifier}`;\n    Object.defineProperty(window, prop, {\n        value: (result) => {\n            if (once) {\n                Reflect.deleteProperty(window, prop);\n            }\n            return callback === null || callback === void 0 ? void 0 : callback(result);\n        },\n        writable: false,\n        configurable: true\n    });\n    return identifier;\n}\n/**\n * Sends a message to the backend.\n * @example\n * ```typescript\n * import { invoke } from '@tauri-apps/api/tauri';\n * await invoke('login', { user: 'tauri', password: 'poiwe3h4r5ip3yrhtew9ty' });\n * ```\n *\n * @param cmd The command name.\n * @param args The optional arguments to pass to the command.\n * @return A promise resolving or rejecting to the backend response.\n *\n * @since 1.0.0\n */\nasync function invoke(cmd, args = {}) {\n    return new Promise((resolve, reject) => {\n        const callback = transformCallback((e) => {\n            resolve(e);\n            Reflect.deleteProperty(window, `_${error}`);\n        }, true);\n        const error = transformCallback((e) => {\n            reject(e);\n            Reflect.deleteProperty(window, `_${callback}`);\n        }, true);\n        window.__TAURI_IPC__({\n            cmd,\n            callback,\n            error,\n            ...args\n        });\n    });\n}\n/**\n * Convert a device file path to an URL that can be loaded by the webview.\n * Note that `asset:` and `https://asset.localhost` must be added to [`tauri.security.csp`](https://tauri.app/v1/api/config/#securityconfig.csp) in `tauri.conf.json`.\n * Example CSP value: `\"csp\": \"default-src 'self'; img-src 'self' asset: https://asset.localhost\"` to use the asset protocol on image sources.\n *\n * Additionally, `asset` must be added to [`tauri.allowlist.protocol`](https://tauri.app/v1/api/config/#allowlistconfig.protocol)\n * in `tauri.conf.json` and its access scope must be defined on the `assetScope` array on the same `protocol` object.\n * For example:\n * ```json\n * {\n *   \"tauri\": {\n *     \"allowlist\": {\n *       \"protocol\": {\n *         \"asset\": true,\n *         \"assetScope\": [\"$APPDATA/assets/*\"]\n *       }\n *     }\n *   }\n * }\n * ```\n *\n * @param  filePath The file path.\n * @param  protocol The protocol to use. Defaults to `asset`. You only need to set this when using a custom protocol.\n * @example\n * ```typescript\n * import { appDataDir, join } from '@tauri-apps/api/path';\n * import { convertFileSrc } from '@tauri-apps/api/tauri';\n * const appDataDirPath = await appDataDir();\n * const filePath = await join(appDataDirPath, 'assets/video.mp4');\n * const assetUrl = convertFileSrc(filePath);\n *\n * const video = document.getElementById('my-video');\n * const source = document.createElement('source');\n * source.type = 'video/mp4';\n * source.src = assetUrl;\n * video.appendChild(source);\n * video.load();\n * ```\n *\n * @return the URL that can be used as source on the webview.\n *\n * @since 1.0.0\n */\nfunction convertFileSrc(filePath, protocol = 'asset') {\n    return window.__TAURI__.convertFileSrc(filePath, protocol);\n}\n\nexport { convertFileSrc, invoke, transformCallback };\n"], "mappings": ";;;AAIA,SAAS,MAAM;AACX,SAAO,OAAO,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC;AAC9D;AASA,SAAS,kBAAkB,UAAU,OAAO,OAAO;AAC/C,QAAM,aAAa,IAAI;AACvB,QAAM,OAAO,IAAI,UAAU;AAC3B,SAAO,eAAe,QAAQ,MAAM;AAAA,IAChC,OAAO,CAAC,WAAW;AACf,UAAI,MAAM;AACN,gBAAQ,eAAe,QAAQ,IAAI;AAAA,MACvC;AACA,aAAO,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,MAAM;AAAA,IAC9E;AAAA,IACA,UAAU;AAAA,IACV,cAAc;AAAA,EAClB,CAAC;AACD,SAAO;AACX;AAeA,eAAe,OAAO,KAAK,OAAO,CAAC,GAAG;AAClC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,WAAW,kBAAkB,CAAC,MAAM;AACtC,cAAQ,CAAC;AACT,cAAQ,eAAe,QAAQ,IAAI,KAAK,EAAE;AAAA,IAC9C,GAAG,IAAI;AACP,UAAM,QAAQ,kBAAkB,CAAC,MAAM;AACnC,aAAO,CAAC;AACR,cAAQ,eAAe,QAAQ,IAAI,QAAQ,EAAE;AAAA,IACjD,GAAG,IAAI;AACP,WAAO,cAAc;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL,CAAC;AACL;AA4CA,SAAS,eAAe,UAAU,WAAW,SAAS;AAClD,SAAO,OAAO,UAAU,eAAe,UAAU,QAAQ;AAC7D;", "names": []}